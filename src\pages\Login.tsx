import { useState, useEffect, useRef } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { toast } from "@/components/ui/sonner";
import { useLanguage } from "@/hooks/useLanguage";
import { useAuth } from "@/hooks/auth/AuthContext";
import { Eye, EyeOff, Key, ShieldCheck, ArrowLeft } from "lucide-react";
import { Loading } from "@/components/ui/loading";
import { supabase } from "@/integrations/supabase/client";
import { Turnstile } from "@marsidev/react-turnstile";
import { InputOTP, InputOTPGroup, InputOTPSlot } from "@/components/ui/input-otp";
import { Separator } from "@/components/ui/separator";

// Key for tracking login status
const LOGIN_IN_PROGRESS_KEY = "login_in_progress";

export default function Login() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { t, isRTL } = useLanguage();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const { 
    isAuthenticated, 
    sessionChecked, 
    loading, 
    login, 
    verifyTwoFactor,
    needsTwoFactor,
    user,
    setTwoFactorComplete,
    clearTwoFactorVerification
  } = useAuth();
  const [notificationsShown, setNotificationsShown] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [captchaToken, setCaptchaToken] = useState("");
  const [captchaError, setCaptchaError] = useState(false);
  
  // Login stages
  const [loginStage, setLoginStage] = useState<'credentials' | '2fa'>('credentials');
  const [otpCode, setOtpCode] = useState("");
  
  // Updated with the actual production site key
  const TURNSTILE_SITE_KEY = "0x4AAAAAABaWWRRhV8b4zFQC"; 
  const turnstileRef = useRef(null);
  
  // Check if the current domain is the production domain
  const isProdDomain = window.location.origin === "https://panel.pegasus-tools.com";
  // If not on prod domain, we'll skip captcha validation

  // Clear login tracking on component mount and if not in the middle of auth flow
  useEffect(() => {
    const loginInProgress = localStorage.getItem(LOGIN_IN_PROGRESS_KEY) === 'true';
    
    // Only clear if not in login flow
    if (!needsTwoFactor && !loginInProgress) {

      clearTwoFactorVerification();
    }
    
    // Clear login in progress flag when component unmounts
    return () => {
      // Only clear if not transitioning to dashboard
      if (window.location.pathname !== '/dashboard') {
        localStorage.removeItem(LOGIN_IN_PROGRESS_KEY);
      }
    };
  }, [clearTwoFactorVerification, needsTwoFactor]);

  useEffect(() => {
    if (notificationsShown || !sessionChecked) return;
    
    setNotificationsShown(true);
    
    const passwordReset = searchParams.get("passwordReset");
    if (passwordReset === "success") {
      toast(t("passwordResetSuccess"), {
        description: t("pleaseLoginWithNewPassword")
      });
    }
    
    const sessionExpired = searchParams.get("sessionExpired");
    if (sessionExpired === "true") {

    }
    
    const loggedOut = searchParams.get("loggedOut");
    if (loggedOut === "true") {
      // No need for any message here
    }
  }, [searchParams, t, notificationsShown, sessionChecked]);

  // Monitor needsTwoFactor state to show 2FA screen
  useEffect(() => {

    if (needsTwoFactor && loginStage === 'credentials') {

      setLoginStage('2fa');
    }
  }, [needsTwoFactor, loginStage]);

  useEffect(() => {
    // Only redirect if user is fully authenticated (passed 2FA if needed)
    if (sessionChecked && isAuthenticated) {

      navigate('/dashboard');
    }
  }, [isAuthenticated, navigate, sessionChecked]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Set login in progress flag
    localStorage.setItem(LOGIN_IN_PROGRESS_KEY, 'true');

    try {
      // Clear any previous 2FA state
      clearTwoFactorVerification();
      setOtpCode('');
      
      // First, check if email exists in any of the account tables
      let accountFound = false;
      let isBlocked = false;
      let hasCredits = true;
      let accountType = '';

      // Check in admins table
      const { data: adminData } = await supabase
        .from('admins')
        .select('email, is_active')
        .eq('email', email)
        .maybeSingle();

      if (adminData) {
        accountFound = true;
        accountType = 'admin';
        isBlocked = !adminData.is_active;
      }

      // Check in distributors table if not found in admins
      if (!accountFound) {
        const { data: distributorData } = await supabase
          .from('distributors')
          .select('email, is_active')
          .eq('email', email)
          .maybeSingle();

        if (distributorData) {
          accountFound = true;
          accountType = 'distributor';
          isBlocked = !distributorData.is_active;
        }
      }

      // Check in users table if not found in admins or distributors
      if (!accountFound) {
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('email, block, credits')
          .eq('email', email)
          .maybeSingle();

        if (userError && userError.code !== 'PGRST116') {
          console.error('Error checking user:', userError);
        } else if (userData) {
          accountFound = true;
          accountType = 'user';
          isBlocked = userData.block === 'Blocked';
          hasCredits = parseFloat(userData.credits || '0') > 0;
        }
      }

      if (accountFound) {
        // Check if account is blocked/inactive
        if (isBlocked) {
          toast(t("accountBlocked"), {
            description: t("accountBlockedDescription")
          });
          setIsSubmitting(false);
          localStorage.removeItem(LOGIN_IN_PROGRESS_KEY);
          return;
        }

        // Check if user has no credits (for regular users only)
        if (accountType === 'user' && !hasCredits) {
          toast(t("noCreditsLeft"), {
            description: t("noCreditsLeftDescription")
          });
          setIsSubmitting(false);
          localStorage.removeItem(LOGIN_IN_PROGRESS_KEY);
          return;
        }
      }

      const result = await login(email, password);
      
      if (result) {

      } else {
        // Login failed
        setIsSubmitting(false);
        localStorage.removeItem(LOGIN_IN_PROGRESS_KEY);
      }
    } catch (err) {

      toast(t("loginFailed") || "Login failed", {
        description: err instanceof Error ? err.message : t("unexpectedError") || "An unexpected error occurred"
      });
      setIsSubmitting(false);
      localStorage.removeItem(LOGIN_IN_PROGRESS_KEY);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleOTPVerify = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    
    if (otpCode.length !== 6 || !user) {
      return;
    }
    
    setIsSubmitting(true);

    try {
      // Use the verifyTwoFactor method from our auth context
      const isValid = await verifyTwoFactor(user.id, otpCode);
      
      if (isValid) {
        // 2FA verification successful
        setTwoFactorComplete();

        // Keep the login in progress flag so we know we're mid-auth flow
      } else {
        // Invalid OTP - message is shown by verifyTwoFactor
        setOtpCode('');
        localStorage.removeItem(LOGIN_IN_PROGRESS_KEY);
      }
    } catch (error) {

      toast(t("verificationFailed") || "Verification failed", {
        description: error instanceof Error ? error.message : t("unexpectedError") || "An unexpected error occurred"
      });
      localStorage.removeItem(LOGIN_IN_PROGRESS_KEY);
    } finally {
      setIsSubmitting(false);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handleCaptchaSolved = (token: string) => {
    setCaptchaToken(token);
    setCaptchaError(false);
  };

  const handleCaptchaError = () => {
    setCaptchaError(true);
    setCaptchaToken("");
  };

  const handleCaptchaExpired = () => {
    setCaptchaToken("");
  };
  
  const handleBack = () => {
    // For 2FA screen: go back to credentials, but only if not in the middle of the auth flow
    if (!user) {
      setLoginStage('credentials');
      setOtpCode('');
      localStorage.removeItem(LOGIN_IN_PROGRESS_KEY);
    } else {
      // If in auth flow with a user, log out and go back to credentials
      supabase.auth.signOut().then(() => {
        setLoginStage('credentials');
        setOtpCode('');
        clearTwoFactorVerification();
        localStorage.removeItem(LOGIN_IN_PROGRESS_KEY);
      });
    }
  };

  if (!sessionChecked || loading) {
    return <Loading text={t("checkingSession") || "جاري التحقق من حالة الجلسة..."} className="min-h-screen" />;
  }

  // Don't render anything if we're already authenticated and should be redirected
  if (isAuthenticated) {
    return null;
  }

  return (
    <div dir={isRTL ? "rtl" : "ltr"} className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8 p-8 bg-white rounded-lg shadow-lg">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900">{t("login")}</h2>
        </div>
        
        {loginStage === 'credentials' ? (
          <form className="mt-8 space-y-6" onSubmit={handleLogin}>
            <div className="space-y-4">
              <div>
                <Label htmlFor="email">{t("email")}</Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  dir={isRTL ? "rtl" : "ltr"}
                  autoComplete="email"
                />
              </div>
              <div>
                <Label htmlFor="password">{t("password")}</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    dir={isRTL ? "rtl" : "ltr"}
                    autoComplete="current-password"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={togglePasswordVisibility}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-gray-500" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-500" />
                    )}
                  </button>
                </div>
              </div>
              {/* Only show captcha on production domain */}
              {isProdDomain && (
                <div className={`flex justify-center ${captchaError ? 'border border-red-500 rounded-md p-2' : ''}`}>
                  <Turnstile
                    ref={turnstileRef}
                    siteKey={TURNSTILE_SITE_KEY}
                    onSuccess={handleCaptchaSolved}
                    onError={handleCaptchaError}
                    onExpire={handleCaptchaExpired}
                    options={{
                      theme: 'light',
                      language: isRTL ? 'ar' : 'en',
                    }}
                  />
                </div>
              )}
            </div>
            <Button
              type="submit"
              disabled={isSubmitting || loading || (isProdDomain && !captchaToken)}
              className="w-full"
            >
              {isSubmitting ? t("loggingIn") : t("login")}
            </Button>
          </form>
        ) : (
          <form className="mt-8 space-y-6" onSubmit={handleOTPVerify}>
            <div className="flex items-center gap-2 mb-6">
              <ShieldCheck className="h-5 w-5 text-primary" />
              <h3 className="text-lg font-semibold">{t("twoFactorAuth") || "Two-Factor Authentication"}</h3>
            </div>
            
            <Separator className="my-4" />
            
            <p className="text-sm text-gray-600">
              {t("enterVerificationCode") || "Enter the verification code from your authenticator app"}
            </p>
            
            <div className="flex flex-col items-center space-y-4">
              <InputOTP 
                maxLength={6} 
                value={otpCode}
                onChange={setOtpCode}
                disabled={isSubmitting}
                className="justify-center"
              >
                <InputOTPGroup>
                  <InputOTPSlot index={0} />
                  <InputOTPSlot index={1} />
                  <InputOTPSlot index={2} />
                  <InputOTPSlot index={3} />
                  <InputOTPSlot index={4} />
                  <InputOTPSlot index={5} />
                </InputOTPGroup>
              </InputOTP>
              
              <p className="text-sm text-muted-foreground text-center">
                {t("useAuthenticatorApp") || "Use your authenticator app to get the code"}
              </p>
            </div>
            
            <div className="pt-2 flex flex-col gap-2">
              <Button 
                type="submit"
                disabled={otpCode.length !== 6 || isSubmitting}
                className="w-full"
              >
                {isSubmitting ? t("verifying") || "جاري التحقق..." : t("verify") || "تحقق"}
              </Button>
              
              <Button
                type="button"
                variant="outline"
                className="w-full flex items-center justify-center gap-2"
                onClick={handleBack}
                disabled={isSubmitting}
              >
                <ArrowLeft size={16} />
                {t("back") || "رجوع"}
              </Button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
}
