
import { useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/components/ui/sonner";
import { useLanguage } from "../useLanguage";
import { AuthActions } from "./types";
import { validate2FAToken } from "@/integrations/supabase/twoFactorAuth";

// Key for tracking login status
const LOGIN_IN_PROGRESS_KEY = "login_in_progress";
const TwoFactorVerifiedKey = "auth_2fa_verified";

export const useAuthActions = (): AuthActions => {
  const navigate = useNavigate();
  const { t } = useLanguage();

  const checkSession = useCallback(async () => {
    try {
      const { data, error } = await supabase.auth.getSession();
      
      if (error) {

        return false;
      }
      
      if (!data.session) {

        return false;
      }
      
      const currentTime = Math.floor(Date.now() / 1000);
      if (data.session.expires_at && data.session.expires_at < currentTime) {

        return false;
      }
      
      return true;
    } catch (err) {

      return false;
    }
  }, []);

  const handleSessionExpired = useCallback(() => {

    // Clear any 2FA verification state
    localStorage.removeItem(TwoFactorVerifiedKey);
    localStorage.removeItem(LOGIN_IN_PROGRESS_KEY);
    
    if (window.location.pathname !== '/login') {
      toast(t("sessionExpired") || "انتهت صلاحية الجلسة", {
        description: t("pleaseLoginAgain") || "يرجى تسجيل الدخول مجددًا"
      });
      
      navigate('/login?sessionExpired=true');
    }
  }, [navigate, t]);

  const login = async (email: string, password: string) => {
    try {

      // Set login in progress
      localStorage.setItem(LOGIN_IN_PROGRESS_KEY, 'true');
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });
      
      if (error) {

        localStorage.removeItem(LOGIN_IN_PROGRESS_KEY);
        throw error;
      }

      // Initial auth is successful, but we need to check account type and 2FA
      const { data: accountData, error: accountError } = await supabase.rpc('get_account_info', {
        user_id: data.session.user.id
      });

      if (accountError || !accountData || accountData.length === 0) {
        console.error('Error fetching account info:', accountError);
        console.error('Account data:', accountData);
        console.error('User ID:', data.session.user.id);
        localStorage.removeItem(LOGIN_IN_PROGRESS_KEY);
        throw new Error(`Account not found or invalid: ${accountError?.message || 'No data returned'}`);
      }

      const { account_type, user_data } = accountData[0];

      // Check if account is blocked (for users)
      if (account_type === 'user' && user_data.block === 'Blocked') {
        localStorage.removeItem(LOGIN_IN_PROGRESS_KEY);
        throw new Error('Account is blocked');
      }

      // Check if admin account is active
      if (account_type === 'admin' && !user_data.is_active) {
        localStorage.removeItem(LOGIN_IN_PROGRESS_KEY);
        throw new Error('Admin account is inactive');
      }

      // Check if distributor account is active
      if (account_type === 'distributor' && !user_data.is_active) {
        localStorage.removeItem(LOGIN_IN_PROGRESS_KEY);
        throw new Error('Distributor account is inactive');
      }

      // Check 2FA requirement
      const twoFactorEnabled = account_type === 'admin' ?
        user_data.two_factor_enabled :
        false; // For now, only admins have 2FA

      // Only show success toast if 2FA is not required
      if (!twoFactorEnabled) {
        toast(t("loginSuccess"), {
          description: t("welcomeBack")
        });

        // No 2FA needed, clear flag and redirect based on account type
        localStorage.removeItem(LOGIN_IN_PROGRESS_KEY);

        // Navigate based on account type
        switch (account_type) {
          case 'admin':
            navigate('/dashboard');
            break;
          case 'distributor':
            navigate('/dashboard'); // Will be changed to distributor dashboard later
            break;
          case 'user':
            navigate('/dashboard');
            break;
          default:
            navigate('/dashboard');
        }
      }
      
      return true;
    } catch (error) {

      toast(t("loginFailed"), {
        description: error instanceof Error ? error.message : t("unexpectedError")
      });
      return false;
    }
  };

  const verifyTwoFactor = async (userId: string, token: string) => {
    try {

      const isValid = await validate2FAToken(userId, token);

      if (isValid) {
        // Store 2FA verification in localStorage
        localStorage.setItem(TwoFactorVerifiedKey, 'true');
        
        toast(t("loginSuccess") || "Login successful", {
          description: t("welcomeBack") || "Welcome back"
        });
        
        // Navigate after small delay to allow state updates
        setTimeout(() => {
          navigate('/dashboard');
        }, 200);
      } else {
        localStorage.removeItem(TwoFactorVerifiedKey);
        
        toast(t("invalidOTP") || "Invalid verification code", {
          description: t("invalidOTPDescription") || "Please try again with the correct code"
        });
      }
      
      return isValid;
    } catch (error) {

      localStorage.removeItem(TwoFactorVerifiedKey);
      
      toast(t("verificationFailed") || "Verification failed", {
        description: error instanceof Error ? error.message : t("unexpectedError") || "An unexpected error occurred"
      });
      return false;
    }
  };

  const logout = async () => {
    try {
      const isSessionValid = await checkSession();
      
      if (!isSessionValid) {

        // Clear all authentication related storage
        localStorage.removeItem(TwoFactorVerifiedKey);
        localStorage.removeItem(LOGIN_IN_PROGRESS_KEY);
        
        navigate('/login?loggedOut=true');
        return true;
      }
      
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      
      // Clear 2FA verification state on logout
      localStorage.removeItem(TwoFactorVerifiedKey); 
      localStorage.removeItem(LOGIN_IN_PROGRESS_KEY);
      
      toast(t("logoutSuccess"), {
        description: t("comeBackSoon")
      });
      
      navigate('/login?loggedOut=true');
      return true;
    } catch (error) {

      toast(t("logoutFailed"), {
        description: error instanceof Error ? error.message : t("unexpectedError")
      });
      return false;
    }
  };

  return {
    login,
    logout,
    checkSession,
    handleSessionExpired,
    verifyTwoFactor
  };
};
