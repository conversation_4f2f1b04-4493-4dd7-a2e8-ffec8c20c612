
import { useState, useCallback, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { AuthUser, UserRole, AuthState } from "./types";
import { Session, AuthChangeEvent } from "@supabase/supabase-js";

// Key for storing 2FA verification state in localStorage
const TwoFactorVerifiedKey = "auth_2fa_verified";

export const useAuthState = (): AuthState => {
  const [loading, setLoading] = useState(true);
  const [role, setRole] = useState<UserRole | null>(null);
  const [user, setUser] = useState<AuthUser | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [sessionChecked, setSessionChecked] = useState(false);
  const [needsTwoFactor, setNeedsTwoFactor] = useState(false);
  // Initialize from localStorage if available
  const [twoFactorVerified, setTwoFactorVerified] = useState<boolean>(() => {
    const stored = localStorage.getItem(TwoFactorVerifiedKey);
    return stored === 'true';
  });

  const fetchUserData = useCallback(async (userId: string): Promise<AuthUser | null> => {
    try {
      // Use the new get_account_info function to determine account type and fetch data
      const { data, error } = await supabase.rpc('get_account_info', {
        user_id: userId
      });

      if (error || !data || data.length === 0) {
        console.error('Error fetching account info:', error);
        return null;
      }

      const accountInfo = data[0];
      const { account_type, user_data } = accountInfo;

      if (!account_type || !user_data) {
        console.error('Invalid account info received');
        return null;
      }

      // Check if user has 2FA enabled (for all account types)
      let hasTwoFactorEnabled = false;

      if (account_type === 'admin') {
        hasTwoFactorEnabled = user_data.two_factor_enabled || false;
      } else {
        // For users and distributors, check the two_factor_verification table
        const { data: twoFactorData, error: twoFactorError } = await supabase
          .from('two_factor_verification')
          .select('enabled')
          .eq('user_id', userId)
          .maybeSingle();
        hasTwoFactorEnabled = !twoFactorError && twoFactorData?.enabled === true;
      }

      setNeedsTwoFactor(hasTwoFactorEnabled);

      // Check if 2FA has been previously verified for this user
      const isVerified = localStorage.getItem(TwoFactorVerifiedKey) === 'true';

      if (hasTwoFactorEnabled) {
        setTwoFactorVerified(isVerified);
      } else {
        // No 2FA needed, so it's "verified" by default
        setTwoFactorVerified(true);
        // Clean up any stored 2FA state if not needed
        localStorage.removeItem(TwoFactorVerifiedKey);
      }

      // Create user object based on account type
      const baseUser: AuthUser = {
        id: userId,
        email: user_data.email,
        name: user_data.name,
        role: account_type as UserRole,
        twoFactorEnabled: hasTwoFactorEnabled
      };

      // Add type-specific fields
      switch (account_type) {
        case 'admin':
          return {
            ...baseUser,
            roleLevel: user_data.role_level,
            permissions: user_data.permissions
          };

        case 'distributor':
          return {
            ...baseUser,
            distributorId: user_data.id,
            distributorLevel: user_data.distributor_level,
            commissionPercentage: user_data.commission_percentage,
            parentDistributorId: user_data.parent_distributor_id,
            canCreateDistributors: user_data.can_create_distributors,
            region: user_data.region,
            totalUsers: user_data.total_users,
            totalCommission: user_data.total_commission
          };

        case 'user':
          return {
            ...baseUser,
            credits: user_data.credits,
            expiryTime: user_data.expiry_time,
            assignedByAdmin: user_data.assigned_by_admin,
            uid: user_data.id
          };

        default:
          console.error('Unknown account type:', account_type);
          return null;
      }
    } catch (err) {
      console.error('Error in fetchUserData:', err);
      return null;
    }
  }, []);

  // Method to mark 2FA as verified
  const setTwoFactorComplete = useCallback(() => {

    setTwoFactorVerified(true);
    // Store 2FA verification status in localStorage to persist across page reloads
    localStorage.setItem(TwoFactorVerifiedKey, 'true');
    setIsAuthenticated(true);
  }, []);

  // Method to clear 2FA verification (used on logout)
  const clearTwoFactorVerification = useCallback(() => {

    localStorage.removeItem(TwoFactorVerifiedKey);
    setTwoFactorVerified(false);
  }, []);

  const handleSession = useCallback(async (session: Session | null) => {
    if (!session) {

      setIsAuthenticated(false);
      setUser(null);
      setRole(null);
      setNeedsTwoFactor(false);
      setTwoFactorVerified(false);
      // Clean up stored 2FA state on session end
      localStorage.removeItem(TwoFactorVerifiedKey);
      return;
    }

    // Fetch user data with a small delay to ensure DB is ready
    setTimeout(async () => {
      const userData = await fetchUserData(session.user.id);
      if (userData) {

        setUser(userData as AuthUser);
        setRole(userData.role as UserRole);
        
        const requiresTwoFactor = userData.twoFactorEnabled || false;

        // Check if 2FA is already verified from localStorage
        const isVerified = localStorage.getItem(TwoFactorVerifiedKey) === 'true';

        // Set authentication state based on 2FA requirements and verification
        const isFullyAuthenticated = !requiresTwoFactor || isVerified;

        setIsAuthenticated(isFullyAuthenticated);
      } else {

        setIsAuthenticated(false);
      }
    }, 500);
  }, [fetchUserData]);

  useEffect(() => {
    let unsubscribe: (() => void) | null = null;
    
    const setupAuthListener = async () => {
      try {

        setLoading(true);
        
        // Set up the auth state listener
        const { data: { subscription } } = supabase.auth.onAuthStateChange(
          (event: AuthChangeEvent, session) => {

            switch(event) {
              case 'SIGNED_OUT':
                setRole(null);
                setUser(null);
                setIsAuthenticated(false);
                setNeedsTwoFactor(false);
                // Important: Clear 2FA verification on sign out
                localStorage.removeItem(TwoFactorVerifiedKey);
                setTwoFactorVerified(false);
                break;
              
              case 'SIGNED_IN':
              case 'TOKEN_REFRESHED':
              case 'USER_UPDATED':
              case 'INITIAL_SESSION':
                if (session) {
                  // Use setTimeout to avoid potential deadlocks with Supabase auth
                  setTimeout(() => {
                    handleSession(session);
                  }, 0);
                }
                break;
            }
          }
        );

        unsubscribe = () => {
          subscription.unsubscribe();
        };

        // Check for existing session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        
        if (sessionError) {

        } else {

          await handleSession(session);
        }
        
      } catch (err) {

      } finally {
        setLoading(false);
        setSessionChecked(true);
      }
    };

    setupAuthListener();

    return () => {
      if (unsubscribe) unsubscribe();
    };
  }, [handleSession]);

  // Sync isAuthenticated whenever 2FA status changes
  useEffect(() => {
    if (user) {
      const isFullyAuthenticated = !needsTwoFactor || twoFactorVerified;

      setIsAuthenticated(isFullyAuthenticated);
    }
  }, [needsTwoFactor, twoFactorVerified, user]);

  return {
    loading,
    role,
    user,
    isAuthenticated,
    isAdmin: role === 'admin',
    sessionChecked,
    needsTwoFactor,
    twoFactorVerified,
    setTwoFactorComplete,
    clearTwoFactorVerification
  };
};
