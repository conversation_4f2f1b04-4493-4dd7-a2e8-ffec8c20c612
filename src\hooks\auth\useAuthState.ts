
import { useState, useCallback, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { AuthUser, UserRole, AuthState } from "./types";
import { Session, AuthChangeEvent } from "@supabase/supabase-js";

// Key for storing 2FA verification state in localStorage
const TwoFactorVerifiedKey = "auth_2fa_verified";

export const useAuthState = (): AuthState => {
  const [loading, setLoading] = useState(true);
  const [role, setRole] = useState<UserRole | null>(null);
  const [user, setUser] = useState<AuthUser | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [sessionChecked, setSessionChecked] = useState(false);
  const [needsTwoFactor, setNeedsTwoFactor] = useState(false);
  // Initialize from localStorage if available
  const [twoFactorVerified, setTwoFactorVerified] = useState<boolean>(() => {
    const stored = localStorage.getItem(TwoFactorVerifiedKey);
    return stored === 'true';
  });

  const fetchUserData = useCallback(async (userId: string) => {
    try {

      // Try first with ID
      const { data: userDataById, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {

      }

      // If found by ID, use it
      if (userDataById) {

        const userRole = ((userDataById.email_type || '').toLowerCase() === 'admin') 
          ? 'admin' as UserRole 
          : 'user' as UserRole;
        
        // Check if user has 2FA enabled
        const hasTwoFactorEnabled = userDataById.two_factor_enabled || false;
        setNeedsTwoFactor(hasTwoFactorEnabled);
        
        // IMPORTANT: Check if 2FA has been previously verified for this user
        const isVerified = localStorage.getItem(TwoFactorVerifiedKey) === 'true';

        if (hasTwoFactorEnabled) {
          setTwoFactorVerified(isVerified);
        } else {
          // No 2FA needed, so it's "verified" by default
          setTwoFactorVerified(true);
          // Clean up any stored 2FA state if not needed
          localStorage.removeItem(TwoFactorVerifiedKey);
        }
        
        return {
          id: userDataById.id,
          email: userDataById.email,
          name: userDataById.name || '',
          role: userRole,
          credits: userDataById.credits,
          expiryTime: userDataById.expiry_time,
          uid: userDataById.uid,
          twoFactorEnabled: hasTwoFactorEnabled
        };
      }

      // If not found by ID, try with UID

      const { data: userDataByUid, error: uidError } = await supabase
        .from('users')
        .select('*')
        .eq('uid', userId)
        .single();
        
      if (uidError) {

        return null;
      }
      
      if (!userDataByUid) {

        return null;
      }

      const userRole = ((userDataByUid.email_type || '').toLowerCase() === 'admin') 
        ? 'admin' as UserRole 
        : 'user' as UserRole;
      
      // Check if user has 2FA enabled
      const hasTwoFactorEnabled = userDataByUid.two_factor_enabled || false;
      setNeedsTwoFactor(hasTwoFactorEnabled);
      
      // IMPORTANT: Check if 2FA has been previously verified for this user
      const isVerified = localStorage.getItem(TwoFactorVerifiedKey) === 'true';

      if (hasTwoFactorEnabled) {
        setTwoFactorVerified(isVerified);
      } else {
        // No 2FA needed, so it's "verified" by default
        setTwoFactorVerified(true);
        // Clean up any stored 2FA state if not needed
        localStorage.removeItem(TwoFactorVerifiedKey);
      }
      
      return {
        id: userDataByUid.id,
        email: userDataByUid.email,
        name: userDataByUid.name || '',
        role: userRole,
        credits: userDataByUid.credits,
        expiryTime: userDataByUid.expiry_time,
        uid: userDataByUid.uid,
        twoFactorEnabled: hasTwoFactorEnabled
      };
    } catch (err) {

      return null;
    }
  }, []);

  // Method to mark 2FA as verified
  const setTwoFactorComplete = useCallback(() => {

    setTwoFactorVerified(true);
    // Store 2FA verification status in localStorage to persist across page reloads
    localStorage.setItem(TwoFactorVerifiedKey, 'true');
    setIsAuthenticated(true);
  }, []);

  // Method to clear 2FA verification (used on logout)
  const clearTwoFactorVerification = useCallback(() => {

    localStorage.removeItem(TwoFactorVerifiedKey);
    setTwoFactorVerified(false);
  }, []);

  const handleSession = useCallback(async (session: Session | null) => {
    if (!session) {

      setIsAuthenticated(false);
      setUser(null);
      setRole(null);
      setNeedsTwoFactor(false);
      setTwoFactorVerified(false);
      // Clean up stored 2FA state on session end
      localStorage.removeItem(TwoFactorVerifiedKey);
      return;
    }

    // Fetch user data with a small delay to ensure DB is ready
    setTimeout(async () => {
      const userData = await fetchUserData(session.user.id);
      if (userData) {

        setUser(userData as AuthUser);
        setRole(userData.role as UserRole);
        
        const requiresTwoFactor = userData.twoFactorEnabled || false;

        // Check if 2FA is already verified from localStorage
        const isVerified = localStorage.getItem(TwoFactorVerifiedKey) === 'true';

        // Set authentication state based on 2FA requirements and verification
        const isFullyAuthenticated = !requiresTwoFactor || isVerified;

        setIsAuthenticated(isFullyAuthenticated);
      } else {

        setIsAuthenticated(false);
      }
    }, 500);
  }, [fetchUserData]);

  useEffect(() => {
    let unsubscribe: (() => void) | null = null;
    
    const setupAuthListener = async () => {
      try {

        setLoading(true);
        
        // Set up the auth state listener
        const { data: { subscription } } = supabase.auth.onAuthStateChange(
          (event: AuthChangeEvent, session) => {

            switch(event) {
              case 'SIGNED_OUT':
                setRole(null);
                setUser(null);
                setIsAuthenticated(false);
                setNeedsTwoFactor(false);
                // Important: Clear 2FA verification on sign out
                localStorage.removeItem(TwoFactorVerifiedKey);
                setTwoFactorVerified(false);
                break;
              
              case 'SIGNED_IN':
              case 'TOKEN_REFRESHED':
              case 'USER_UPDATED':
              case 'INITIAL_SESSION':
                if (session) {
                  // Use setTimeout to avoid potential deadlocks with Supabase auth
                  setTimeout(() => {
                    handleSession(session);
                  }, 0);
                }
                break;
            }
          }
        );

        unsubscribe = () => {
          subscription.unsubscribe();
        };

        // Check for existing session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        
        if (sessionError) {

        } else {

          await handleSession(session);
        }
        
      } catch (err) {

      } finally {
        setLoading(false);
        setSessionChecked(true);
      }
    };

    setupAuthListener();

    return () => {
      if (unsubscribe) unsubscribe();
    };
  }, [handleSession]);

  // Sync isAuthenticated whenever 2FA status changes
  useEffect(() => {
    if (user) {
      const isFullyAuthenticated = !needsTwoFactor || twoFactorVerified;

      setIsAuthenticated(isFullyAuthenticated);
    }
  }, [needsTwoFactor, twoFactorVerified, user]);

  return {
    loading,
    role,
    user,
    isAuthenticated,
    isAdmin: role === 'admin',
    sessionChecked,
    needsTwoFactor,
    twoFactorVerified,
    setTwoFactorComplete,
    clearTwoFactorVerification
  };
};
