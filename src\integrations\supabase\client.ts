
// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';
import * as OTPAuth from 'otplib';
import QRCode from 'qrcode';

const SUPABASE_URL = "https://sxigocnatqgqgiedrgue.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InN4aWdvY25hdHFncWdpZWRyZ3VlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNTY1ODgsImV4cCI6MjA2MDgzMjU4OH0.JaRFyEuVOC9VXoPFc7ohO77F1qM_NwY_jOgNcSacfp4";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true
  },
  global: {
    headers: {
      'X-Client-Info': 'supabase-js-web'
    }
  }
});

// 2FA Functions
export const generate2FASecret = async (userId: string, email: string) => {
  try {
    // Generate a random secret
    const secret = OTPAuth.authenticator.generateSecret();
    
    // Create the OTP Auth URL
    const otpAuthUrl = OTPAuth.authenticator.keyuri(
      email,
      'Pegasus Tools',
      secret
    );
    
    // Generate QR code
    const qrCodeDataUrl = await QRCode.toDataURL(otpAuthUrl);
    
    // Store the secret in the database
    const { error } = await supabase
      .from('users')
      .update({ 
        otp_secret: secret,
        two_factor_enabled: false // Will be enabled after verification
      })
      .eq('id', userId);
    
    if (error) {
      throw error;
    }
    
    return {
      secret,
      qrCodeDataUrl,
      otpAuthUrl
    };
  } catch (error) {
    throw error;
  }
};

export const verify2FAToken = async (userId: string, token: string) => {
  try {
    // Get the user's secret from the database
    const { data: userData, error } = await supabase
      .from('users')
      .select('otp_secret')
      .eq('id', userId)
      .single();
    
    if (error || !userData?.otp_secret) {
      return false;
    }
    
    // Verify the token
    const isValid = OTPAuth.authenticator.verify({
      token,
      secret: userData.otp_secret
    });
    
    if (isValid) {
      // Enable 2FA for the user
      const { error: updateError } = await supabase
        .from('users')
        .update({ two_factor_enabled: true })
        .eq('id', userId);
      
      if (updateError) {
        return false;
      }
    }
    
    return isValid;
  } catch (error) {
    return false;
  }
};

export const validate2FAToken = async (userId: string, token: string) => {
  try {
    // Get the user's secret from the database
    const { data: userData, error } = await supabase
      .from('users')
      .select('otp_secret, two_factor_enabled')
      .eq('id', userId)
      .single();
    
    if (error || !userData?.otp_secret) {
      return false;
    }
    
    if (!userData.two_factor_enabled) {
      return false;
    }
    
    // Verify the token
    const isValid = OTPAuth.authenticator.verify({
      token,
      secret: userData.otp_secret
    });
    
    return isValid;
  } catch (error) {
    return false;
  }
};

export const disable2FA = async (userId: string) => {
  try {
    // Remove the secret and disable 2FA
    const { error } = await supabase
      .from('users')
      .update({ 
        otp_secret: null,
        two_factor_enabled: false
      })
      .eq('id', userId);
    
    if (error) {
      return false;
    }
    
    return true;
  } catch (error) {
    return false;
  }
};

export const saveQRCodeFile = async (userId: string, qrCodeDataUrl: string) => {
  try {
    // Convert data URL to blob
    const response = await fetch(qrCodeDataUrl);
    const blob = await response.blob();
    
    // Create a download link
    const url = URL.createObjectURL(blob);
    
    return {
      success: true,
      url: url
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};
